// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const contactForm = document.getElementById('contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            let isValid = true;
            
            // Basic form validation
            const name = document.getElementById('name');
            const email = document.getElementById('email');
            const phone = document.getElementById('phone');
            
            // Reset error states
            const formInputs = contactForm.querySelectorAll('input, textarea, select');
            formInputs.forEach(input => {
                input.style.borderColor = '#ccc';
            });
            
            // Validate name
            if (!name.value.trim()) {
                name.style.borderColor = '#f44336';
                isValid = false;
            }
            
            // Validate email
            if (!email.value.trim() || !isEmailValid(email.value)) {
                email.style.borderColor = '#f44336';
                isValid = false;
            }
            
            // Validate phone
            if (!phone.value.trim()) {
                phone.style.borderColor = '#f44336';
                isValid = false;
            }
            
            if (!isValid) {
                e.preventDefault();
                alert('Please fill in all required fields correctly.');
            }
        });
    }
    
    // Smooth scroll for anchor links
    const navLinks = document.querySelectorAll('nav a[href^="#"]');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const targetId = this.getAttribute('href');
            
            if (targetId === '#') return;
            
            e.preventDefault();
            
            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                window.scrollTo({
                    top: targetElement.offsetTop - 100,
                    behavior: 'smooth'
                });
            }
        });
    });
    
    // Mobile navigation toggle
    const menuToggle = document.querySelector('.menu-toggle');
    if (menuToggle) {
        menuToggle.addEventListener('click', function() {
            const nav = document.querySelector('nav ul');
            nav.classList.toggle('show');
        });
    }
    
    // Add animation on scroll
    const animatedElements = document.querySelectorAll('.service, .testimonial, .package');
    
    // Check if element is in viewport
    function isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.bottom >= 0
        );
    }
    
    // Function to add animation class when elements come into view
    function handleScrollAnimation() {
        animatedElements.forEach(elem => {
            if (isInViewport(elem)) {
                elem.classList.add('animate');
            }
        });
    }
    
    // Check elements on scroll
    window.addEventListener('scroll', handleScrollAnimation);
    handleScrollAnimation(); // Initial check
});

// Helper function to validate email format
function isEmailValid(email) {
    const regex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
    return regex.test(email);
}
