/* CSS Reset and General Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #b76e79;
    --primary-dark: #a25c67;
    --text-light: #ffffff;
    --text-dark: #333333;
    --max-width: 1200px;
    --header-height: 70px;
    --section-padding: 80px 20px;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', sans-serif;
    background-color: #fff;
    color: var(--text-dark);
    line-height: 1.6;
    overflow-x: hidden;
    min-height: 100vh;
    width: 100%;
}

/* Header & Navigation */
header {
    background-color: var(--primary-color);
    width: 100%;
    height: var(--header-height);
    padding: 0 max(20px, calc((100% - var(--max-width)) / 2));
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: var(--text-light);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.logo {
    font-size: clamp(1.5rem, 2vw, 2rem);
    font-weight: bold;
}

.menu-toggle {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    z-index: 1001;
}

.hamburger {
    display: block;
    width: 24px;
    height: 2px;
    background: var(--text-light);
    position: relative;
    transition: all 0.3s ease;
}

.hamburger::before,
.hamburger::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: var(--text-light);
    transition: all 0.3s ease;
}

.hamburger::before {
    top: -8px;
}

.hamburger::after {
    bottom: -8px;
}

.menu-toggle.active .hamburger {
    background: transparent;
}

.menu-toggle.active .hamburger::before {
    transform: rotate(45deg);
    top: 0;
}

.menu-toggle.active .hamburger::after {
    transform: rotate(-45deg);
    bottom: 0;
}

.nav-menu {
    height: 100%;
}

.nav-header {
    display: none;
    padding: 1rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.menu-close {
    display: none;
    background: none;
    border: none;
    color: var(--text-light);
    font-size: 2rem;
    cursor: pointer;
}

.nav-footer {
    display: none;
    padding: 1rem;
    text-align: center;
    color: var(--text-light);
    border-top: 1px solid rgba(255,255,255,0.1);
}

nav ul {
    list-style: none;
    display: flex;
    gap: clamp(1rem, 2vw, 2rem);
    margin: 0;
    padding: 0;
    height: 100%;
    align-items: center;
}

nav ul li a {
    text-decoration: none;
    color: var(--text-light);
    font-weight: 500;
    padding: 0.8rem 1.2rem;
    border-radius: 4px;
    transition: all 0.3s ease;
    font-size: clamp(0.9rem, 1.5vw, 1rem);
    display: block;
}

nav ul li a:hover {
    background-color: rgba(255,255,255,0.1);
}

.nav-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 998;
}

/* Hero Section */
.hero {
    position: fixed;
    height: 100vh;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2;
}

.hero-content {
    position: relative;
    z-index: 3;
    text-align: center;
    color: #fff;
    max-width: 800px;
    padding: 0 20px;
    margin-top: 70px;
}

.hero h1 {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero p {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.btn {
    display: inline-block;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    text-decoration: none;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #b76e79;
    color: #fff;
    border: none;
}

.btn-primary:hover {
    background-color: #a25c67;
    transform: translateY(-2px);
}

.btn-outline {
    background-color: transparent;
    color: #fff;
    border: 2px solid #fff;
}

.btn-outline:hover {
    background-color: #fff;
    color: #b76e79;
    transform: translateY(-2px);
}

/* Responsive Styles */
@media screen and (max-width: 1024px) {
    :root {
        --section-padding: 60px 20px;
    }
    
    .hero {
        background-attachment: scroll;
    }
}

@media screen and (max-width: 768px) {
    .menu-toggle {
        display: block;
    }

    .nav-menu {
        position: fixed;
        top: 0;
        right: -300px;
        width: 300px;
        height: 100vh;
        background-color: var(--primary-color);
        padding: 0;
        z-index: 999;
        transition: right 0.3s ease;
        display: flex;
        flex-direction: column;
    }

    .nav-menu.active {
        right: 0;
    }

    .nav-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .nav-header .logo {
        font-size: 1.2rem;
    }

    .menu-close {
        display: block;
    }

    nav ul {
        flex-direction: column;
        height: auto;
        padding: 1rem 0;
        gap: 0;
        flex: 1;
    }

    nav ul li {
        width: 100%;
    }

    nav ul li a {
        padding: 1rem 2rem;
        border-radius: 0;
        width: 100%;
    }

    nav ul li a:hover {
        background-color: var(--primary-dark);
    }

    .nav-footer {
        display: block;
    }

    .nav-overlay.active {
        display: block;
    }

    nav ul li {
        width: 100%;
    }

    nav ul li a {
        display: block;
        padding: 1rem 2rem;
        border-radius: 0;
    }

    .hero-buttons {
        flex-direction: column;
        width: 100%;
        max-width: 300px;
        margin: 0 auto;
    }

    .cta-button {
        width: 100%;
        text-align: center;
    }
}

@media screen and (max-width: 480px) {
    :root {
        --section-padding: 40px 15px;
        --header-height: 60px;
    }

    .logo {
        font-size: 1.25rem;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .hero p {
        font-size: 1rem;
    }

    .cta-button {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }
}

/* Utility Classes */
.container {
    width: 100%;
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 0 20px;
}

.section-padding {
    padding: var(--section-padding);
}

/* Animation Classes */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out forwards;
}

/* Hero Section */
.hero {
    text-align: center;
    color: var(--text-light);
    height: 100vh;
    width: 100vw;
    margin: 0;
    padding: 0;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-image: url('img/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    overflow: hidden;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: -1;
}

.hero-content {
    width: 100%;
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 0 20px;
    margin-top: var(--header-height);
    position: relative;
    z-index: 2;
}

.hero h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    margin-bottom: clamp(1rem, 2vw, 2rem);
    line-height: 1.2;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero p {
    font-size: clamp(1.1rem, 2vw, 1.5rem);
    max-width: 800px;
    margin: 0 auto clamp(2rem, 4vw, 3rem);
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.hero-buttons {
    display: flex;
    gap: clamp(1rem, 2vw, 2rem);
    justify-content: center;
    flex-wrap: wrap;
}

/* Button Styles */
.cta-button {
    display: inline-block;
    padding: 1rem 2rem;
    background-color: var(--primary-color);
    color: var(--text-light);
    text-decoration: none;
    border-radius: 5px;
    font-weight: 600;
    font-size: clamp(0.9rem, 1.5vw, 1rem);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.cta-button:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
}

.cta-secondary {
    background-color: transparent;
    border: 2px solid var(--text-light);
}

.cta-secondary:hover {
    background-color: var(--text-light);
    color: var(--primary-color);
}

.hero h1, .hero p, .hero .cta-button, .hero-buttons {
    position: relative;
    z-index: 2;
}

.hero h1 {
    font-size: 3.5rem;
    font-weight: bold;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.hero p {
    font-size: 1.5rem;
    max-width: 800px;
    margin: 0 auto 30px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.hero-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
}

/* Call to Action Buttons */
.cta-button {
    display: inline-block;
    padding: 15px 30px;
    background-color: #b76e79;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.cta-button:hover {
    background-color: #a25c67;
    transform: translateY(-2px);
}

.cta-secondary {
    background-color: transparent;
    border: 2px solid white;
}

.cta-secondary:hover {
    background-color: white;
    color: #b76e79;
}

/* Page Header */
.page-header {
    background-color: #f6e4e6;
    text-align: center;
    padding: 50px 20px;
}

.page-header h1 {
    font-size: 36px;
    color: #b76e79;
    margin-bottom: 10px;
    margin-top: 30px;
}

/* Section Styling */
section {
    padding: 60px 20px;
    max-width: 1200px;
    margin: 0 auto;
}

#services {
    margin-top: 0;
    padding-top: 80px;
}

h2 {
    color: #b76e79;
    text-align: center;
    margin-bottom: 30px;
    font-size: 32px;
}

h3 {
    color: #b76e79;
    margin-top: 0;
}

/* Services Section */
.service-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    gap: 30px;
}

.service {
    background-color: #f6e4e6;
    padding: 25px;
    border-radius: 10px;
    flex: 1;
    min-width: 250px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.service:hover {
    transform: translateY(-5px);
}

.service-link {
    display: inline-block;
    background-color: #b76e79;
    color: white;
    padding: 8px 15px;
    text-decoration: none;
    border-radius: 20px;
    margin-top: 10px;
    font-size: 14px;
    transition: background-color 0.3s;
}

.service-link:hover {
    background-color: #a35664;
}

/* Testimonials */
.testimonial-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
}

.testimonial {
    background-color: #f6e4e6;
    padding: 25px;
    border-radius: 10px;
    flex: 1;
    min-width: 250px;
    max-width: 500px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    position: relative;
}

.testimonial p {
    font-style: italic;
}

.testimonial cite {
    display: block;
    text-align: right;
    font-weight: bold;
    margin-top: 15px;
}

/* Contact Form */
.contact-section {
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
    justify-content: space-between;
}

form {
    flex: 1;
    min-width: 300px;
}

.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

input, select, textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ccc;
    border-radius: 6px;
    font-family: inherit;
    font-size: 16px;
}

textarea {
    resize: vertical;
}

.submit-btn {
    background-color: #b76e79;
    color: white;
    padding: 12px 25px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

.submit-btn:hover {
    background-color: #a35664;
}

.contact-info {
    flex: 1;
    min-width: 300px;
    background-color: #f6e4e6;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

/* Event Packages */
.packages {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
    padding: 20px;
}

.package {
    background: white;
    border-radius: 10px;
    box-shadow: 0 0 15px rgba(0,0,0,0.1);
    width: 300px;
    padding: 25px;
    text-align: center;
    transition: transform 0.3s;
}

.package:hover {
    transform: translateY(-10px);
}

.package img {
    width: 100%;
    height: 180px;
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: 15px;
}

.package h3 {
    margin: 15px 0;
    color: #b76e79;
}

.package ul {
    text-align: left;
    padding-left: 20px;
    margin-bottom: 20px;
}

.package li {
    margin-bottom: 8px;
}

/* Footer */
footer {
    background-color: #333;
    color: white;
    padding: 40px 20px 20px;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    max-width: 1200px;
    margin: 0 auto;
    gap: 40px;
}

.footer-section {
    flex: 1;
    min-width: 250px;
}

.footer-section h3 {
    color: white;
    margin-bottom: 15px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    padding-bottom: 10px;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social {
    color: white;
    text-decoration: none;
    transition: color 0.3s;
}

.social:hover {
    color: #f6e4e6;
}

.copyright {
    text-align: center;
    margin-top: 40px;
    padding-top: 20px;
    border-top: 1px solid rgba(255,255,255,0.1);
}

/* Media Queries for Responsiveness */
@media (max-width: 768px) {
    header {
        flex-direction: column;
        text-align: center;
    }
    
    nav ul {
        margin-top: 15px;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .hero h1 {
        font-size: 36px;
    }
    
    .hero p {
        font-size: 18px;
    }
    
    .service {
        min-width: 100%;
    }
    
    .package {
        width: 100%;
    }
    
    .menu-toggle {
        display: block;
    }
    
    nav ul {
        display: none;
    }
    
    nav ul.show {
        display: flex;
        flex-direction: column;
        width: 100%;
    }
    
    .cta-section {
        padding: 40px 20px;
    }
}

@media (max-width: 480px) {
    nav ul {
        flex-direction: column;
        gap: 10px;
        align-items: center;
    }
    
    .hero {
        padding: 60px 15px;
    }
    
    .hero h1 {
        font-size: 28px;
    }
    
    section {
        padding: 40px 15px;
    }
}

/* Content Section */
.content-section {
    max-width: 1200px;
    margin: 40px auto;
    padding: 0 20px;
}

.intro-text {
    font-size: 18px;
    line-height: 1.8;
    max-width: 800px;
    margin: 0 auto 40px;
    text-align: center;
}

/* Package Price */
.price {
    font-weight: bold;
    color: #b76e79;
    margin: 10px 0;
}

/* CTA Section */
.cta-section {
    background-color: #f6e4e6;
    text-align: center;
    padding: 60px 20px;
    margin-top: 60px;
}

.cta-section h2 {
    margin-bottom: 20px;
}

.cta-section p {
    max-width: 600px;
    margin: 0 auto 30px;
    font-size: 18px;
}

/* Animation Classes */
.animate {
    animation: fadeInUp 0.8s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Testimonials Section */
.testimonials {
    background-color: #fff;
    padding: 60px 20px;
}

/* Responsive Styles */
@media screen and (max-width: 768px) {
    .menu-toggle {
        display: block;
    }

    nav ul {
        display: none;
        position: absolute;
        top: 70px;
        left: 0;
        right: 0;
        background-color: #b76e79;
        flex-direction: column;
        padding: 20px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    nav ul.show {
        display: flex;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .hero p {
        font-size: 1.2rem;
    }

    .hero-buttons {
        flex-direction: column;
        gap: 10px;
    }

    .service-container {
        flex-direction: column;
    }

    .service {
        margin: 10px 0;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
    }

    .footer-section {
        margin: 20px 0;
    }
}

@media screen and (max-width: 480px) {
    .hero h1 {
        font-size: 2rem;
    }

    .hero p {
        font-size: 1rem;
    }

    .page-header h1 {
        font-size: 28px;
    }

    section {
        padding: 40px 15px;
    }

    .contact-section {
        flex-direction: column;
    }

    .form-group {
        margin-bottom: 15px;
    }
}
