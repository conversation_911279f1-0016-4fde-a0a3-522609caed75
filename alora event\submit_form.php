<?php
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Collect form data
    $name = isset($_POST['name']) ? htmlspecialchars($_POST['name']) : '';
    $email = isset($_POST['email']) ? filter_var($_POST['email'], FILTER_SANITIZE_EMAIL) : '';
    $phone = isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : '';
    $event = isset($_POST['event']) ? htmlspecialchars($_POST['event']) : '';
    $details = isset($_POST['details']) ? htmlspecialchars($_POST['details']) : '';
    
    // Validate data
    $errors = [];
    
    if (empty($name)) {
        $errors[] = "Name is required";
    }
    
    if (empty($email)) {
        $errors[] = "Email is required";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Invalid email format";
    }
    
    if (empty($phone)) {
        $errors[] = "Phone number is required";
    }
    
    if (empty($event)) {
        $errors[] = "Event type is required";
    }
    
    // If no errors, proceed with email sending
    if (empty($errors)) {
        $to = "<EMAIL>"; // Change to your email address
        $subject = "New Event Booking Inquiry: $event";
        
        $message = "
        <html>
        <head>
            <title>New Event Booking Inquiry</title>
        </head>
        <body>
            <h2>New Event Booking Inquiry</h2>
            <p><strong>Name:</strong> $name</p>
            <p><strong>Email:</strong> $email</p>
            <p><strong>Phone:</strong> $phone</p>
            <p><strong>Event Type:</strong> $event</p>
            <p><strong>Details:</strong><br>$details</p>
        </body>
        </html>
        ";
        
        // Set content-type header for sending HTML email
        $headers = "MIME-Version: 1.0" . "\r\n";
        $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
        $headers .= "From: $email" . "\r\n";
        
        // Send email
        $mailSent = mail($to, $subject, $message, $headers);
        
        if ($mailSent) {
            $responseStatus = "success";
            $responseMessage = "Thank you for contacting Alora Events. We'll get back to you shortly!";
        } else {
            $responseStatus = "error";
            $responseMessage = "Sorry, there was an error sending your message. Please try again later.";
        }
    } else {
        $responseStatus = "error";
        $responseMessage = "Please correct the following errors: " . implode(", ", $errors);
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Form Submission - Alora Events</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .response {
            max-width: 600px;
            margin: 100px auto;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .success {
            background-color: #e8f5e9;
            border-left: 5px solid #4caf50;
        }
        
        .error {
            background-color: #ffebee;
            border-left: 5px solid #f44336;
        }
        
        .response h1 {
            margin-top: 0;
            color: #b76e79;
        }
        
        .buttons {
            margin-top: 30px;
        }
        
        .button {
            display: inline-block;
            padding: 10px 20px;
            margin: 0 10px;
            background-color: #b76e79;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        
        .button:hover {
            background-color: #a35664;
        }
    </style>
</head>
<body>
    <header>
        <div class="logo">Alora Events</div>
        <button class="menu-toggle">☰</button>
        <nav>
            <ul>
                <li><a href="index.html">Home</a></li>
                <li><a href="wedding.html">Wedding</a></li>
                <li><a href="birthday.html">Birthday</a></li>
                <li><a href="business.html">Business</a></li>
                <li><a href="engagement.html">Engagement</a></li>
                <li><a href="contact.html">Contact Us</a></li>
            </ul>
        </nav>
    </header>

    <div class="response <?php echo isset($responseStatus) ? $responseStatus : ''; ?>">
        <h1><?php echo isset($responseStatus) && $responseStatus === "success" ? "Thank You!" : "Oops!"; ?></h1>
        <p><?php echo isset($responseMessage) ? $responseMessage : "Something went wrong with your submission."; ?></p>
        
        <div class="buttons">
            <a href="index.html" class="button">Back to Home</a>
            <?php if (isset($responseStatus) && $responseStatus === "error"): ?>
            <a href="contact.html" class="button">Try Again</a>
            <?php endif; ?>
        </div>
    </div>

    <footer>
        <div class="footer-content">
            <div class="footer-section">
                <h3>Contact Us</h3>
                <p>Email: <EMAIL></p>
                <p>Phone: +94 74 187 4003</p>
            </div>
            <div class="footer-section">
                <h3>Follow Us</h3>
                <div class="social-links">
                    <a href="#" class="social">Facebook</a>
                    <a href="#" class="social">Instagram</a>
                    <a href="#" class="social">Pinterest</a>
                </div>
            </div>
        </div>
        <div class="copyright">
            <p>&copy; 2023 Alora Event Planners. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>
