submit_form.php
<?php
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $name = $_POST['name'];
    $email = $_POST['email'];
    $phone = $_POST['phone'];
    $event = $_POST['event'];
    $details = $_POST['details'];

    $to = "<EMAIL>"; // Change to your email address
    $subject = "New Event Booking Inquiry";
    $message = "New booking inquiry:\n\nName: $name\nEmail: $email\nPhone: $phone\nEvent: $event\nDetails: $details";
    $headers = "From: $email";

    if (mail($to, $subject, $message, $headers)) {
        echo "<h1>Thank you! Your form has been submitted.</h1>";
    } else {
        echo "<h1>Sorry, something went wrong. Please try again later.</h1>";
    }
}
?>
